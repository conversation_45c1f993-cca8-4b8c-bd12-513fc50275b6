/* Bottom Footer - Matching the exact design from image */

.bottom-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    padding: 1rem 2rem;
    z-index: 100;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* Language Selector */
.language-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.9rem;
    cursor: pointer;
    user-select: none;
}

.globe-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
    flex-shrink: 0;
}

.language-text {
    font-weight: 400;
}

.dropdown-arrow {
    font-size: 0.7rem;
    color: #9ca3af;
    margin-left: 0.25rem;
}

.language-selector:hover {
    color: #374151;
}

.language-selector:hover .globe-icon {
    color: #374151;
}

/* Footer Links */
.footer-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.footer-links a {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 400;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bottom-footer {
        padding: 1rem;
    }

    .footer-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-links {
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .footer-links {
        flex-direction: column;
        gap: 0.5rem;
    }
}
