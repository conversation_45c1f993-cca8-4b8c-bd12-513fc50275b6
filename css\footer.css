/* Footer tweaks to match screenshot layout */

.footer {
    background: #ffffff;
    border-top: 1px solid #eef0f2;
    padding: 1rem 0.5rem;
    position: relative;
    width: 100%;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links { display: flex; gap: 1.75rem; }
.footer-links a { color: #6b7280; text-decoration: none; font-size: 0.95rem; }
.footer-links a:hover { color: #374151; }

.language-dropdown { position: relative; }
.lang-button {
    display: inline-flex;
    gap: 0.5rem;
    align-items: center;
    border: none;
    background: none;
    color: #374151;
    font-size: 0.95rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
}

.lang-button:focus { outline: 2px solid rgba(15,23,36,0.06); }

.caret { color: #9ca3af; margin-left: 0.25rem; }

.lang-list {
    position: absolute;
    left: 0;
    bottom: calc(100% + 8px);
    background: #fff;
    border: 1px solid #e6e9ee;
    box-shadow: 0 6px 18px rgba(15,23,36,0.06);
    border-radius: 6px;
    list-style: none;
    padding: 0.5rem 0;
    display: none;
    min-width: 160px;
}

.lang-list.open { display: block; }
.lang-list li { padding: 0.5rem 1rem; cursor: pointer; color: #374151; }
.lang-list li:hover { background: #f8fafc; }

@media (max-width: 768px) {
    .footer-container { padding: 0 1rem; flex-direction: column; gap: 0.5rem; }
}
