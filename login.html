<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in to COAST</title>
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    
    <header class="header">
    <div class="header-container">
        <a href="index.html" class="logo">
            <div class="logo-icon">C</div>
            <span class="logo-text">COAST</span>
        </a>

        <div class="header-right">
            <a href="index.html" class="back-link">← Back to Home</a>
        </div>
    </div>
</header>


    <!-- Main Login Container -->
    <main class="page-content">
        <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">Sign in to COAST</h1>
            <p class="login-subtitle">Secure login with MFA and SSO available</p>
        </div>

        <form id="loginForm" class="login-form">
            <div class="form-group">
                <label for="email" class="form-label">Email / Username</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    class="form-input"
                    placeholder="Enter your email or username"
                    required
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="form-input"
                    placeholder="Enter your password"
                    required
                >
            </div>

            <button type="submit" class="btn btn-primary">Log In</button>
        </form>

        <div class="divider">Or continue with</div>

        <button type="button" class="btn btn-secondary" id="ssoLogin">
            🏢 Log in with SSO
        </button>

        <div class="forgot-password">
            <a href="password_reset.html">Forgot password?</a>
        </div>
        </div>
    </main>

    <!-- Bottom Footer (fixed) -->
    <footer class="footer bottom-footer" role="contentinfo">
        <div class="footer-container">
            <div class="language-dropdown" id="languageDropdown">
                <button class="lang-button" aria-haspopup="true" aria-expanded="false" id="langBtn">
                    <svg width="18" height="18" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">
                        <circle cx="12" cy="12" r="10" stroke="#9ca3af" stroke-width="1" fill="white"/>
                        <path d="M2 12h20M12 2c3 4 3 16 0 20c-3-4-3-16 0-20z" stroke="#9ca3af" stroke-width="1"/>
                    </svg>
                    <span id="langLabel">English</span>
                    <span class="caret">▾</span>
                </button>

                <ul class="lang-list" id="langList" role="menu" aria-label="Language selector">
                    <li role="menuitem" data-lang="en">English</li>
                    <li role="menuitem" data-lang="fr">Français</li>
                    <li role="menuitem" data-lang="de">Deutsch</li>
                </ul>
            </div>

            <nav class="footer-links" aria-label="Footer links">
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
            </nav>
        </div>
    </footer>

    <script>
        (function(){
            const btn = document.getElementById('langBtn');
            const list = document.getElementById('langList');
            const label = document.getElementById('langLabel');

            function closeList(){
                list.classList.remove('open');
                btn.setAttribute('aria-expanded','false');
            }

            btn.addEventListener('click', function(e){
                const open = list.classList.toggle('open');
                btn.setAttribute('aria-expanded', open ? 'true' : 'false');
            });

            list.addEventListener('click', function(e){
                const li = e.target.closest('li[data-lang]');
                if(!li) return;
                label.textContent = li.textContent;
                closeList();
            });

            // close when clicking outside
            document.addEventListener('click', function(e){
                if(!document.getElementById('languageDropdown').contains(e.target)) closeList();
            });
        })();
    </script>
</body>
</html>