/* Password Forget Page Specific Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f6f7f8;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    color: #111827;
}

/* prevent vertical page scroll so page does not move up/down */
html, body { height: 100%; }

/* Make page a column flex layout so footer sits at the bottom and content can center vertically */
body { display: flex; flex-direction: column; }

/* allow the main content area to grow and center the card vertically */
.page-content { 
    flex: 1 0 auto; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    padding: 2rem 0 5rem 0; /* Add bottom padding for fixed footer */
}

/* Main Container */
.login-container {
    width: 100%;
    max-width: 430px;
    margin: 0 auto;
    background: #ffffff;
    padding: 2.5rem 2.25rem;
    border-radius: 8px;
    box-shadow: 0 6px 18px rgba(15, 23, 36, 0.06);
}

/* Password Recovery Header */
.password-recovery-header {
    text-align: center;
    margin-bottom: 2rem;
}

.key-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.key-icon-circle {
    width: 64px;
    height: 64px;
    background-color: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.key-icon svg {
    color: #6b7280;
}

.recovery-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f1724;
    margin-bottom: 2rem;
}

/* Info Notice - contains both main instruction and secondary info */
.info-notice {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    margin-bottom: 2rem;
    text-align: center;
}

/* Main instruction text - darker and more prominent */
.main-instruction {
    color: #374151;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
    font-weight: 550;
}

/* Secondary info section */
.secondary-info {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    justify-content: center;
}

.info-icon {
    color: #6b7280;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.info-text {
    color: #6b7280;
    font-size: 0.9rem;
    line-height: 1.4;
    text-align: left;
}

/* Button Styles */
.btn {
    display: block;
    width: 100%;
    padding: 0.875rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.btn-primary {
    background-color: #1a1a1a;
    color: white;
}

.btn-primary:hover {
    background-color: #333;
}

.back-to-login-btn {
    margin-bottom: 2rem;
}

/* Support Links */
.support-links {
    text-align: center;
}

.support-text {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.support-options {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.support-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.7rem;
    transition: color 0.2s ease;
}

.support-link:hover {
    color: #374151;
}

.support-link svg {
    flex-shrink: 0;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container { 
        margin: 3.5rem 1rem; 
        padding: 1.75rem; 
    }
    
    .recovery-title { 
        font-size: 1.25rem; 
    }
    
    .key-icon-circle {
        width: 56px;
        height: 56px;
    }
    
    .key-icon svg {
        width: 20px;
        height: 20px;
    }
}

@media (max-width: 480px) {
    .login-container { 
        margin: 2rem 0.75rem; 
        padding: 1.25rem; 
    }
    
    .recovery-title { 
        font-size: 1.125rem; 
    }
    
    .support-options {
        flex-direction: column;
        gap: 1rem;
    }

    .main-instruction,
    .info-text {
        font-size: 0.85rem;
    }

    .secondary-info {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .info-text {
        text-align: center;
    }
    
    .key-icon-circle {
        width: 48px;
        height: 48px;
    }
    
    .key-icon svg {
        width: 18px;
        height: 18px;
    }
}
