/* Login Page Specific Styles - Matching the provided design */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f6f7f8;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    color: #111827;
}

/* prevent vertical page scroll so page does not move up/down */
html, body { height: 100%; }

/* Make page a column flex layout so footer sits at the bottom and content can center vertically */
body { display: flex; flex-direction: column; }

/* allow the main content area to grow and center the card vertically */
.page-content { flex: 1 0 auto; display: flex; align-items: center; justify-content: center; padding: 2rem 0; }

/* Main Login Container */
/* center the card vertically and keep header space */
.login-container {
    width: 100%;
    max-width: 520px;
    margin: 0 auto;
    background: #ffffff;
    padding: 2.5rem 2.25rem;
    border-radius: 8px;
    box-shadow: 0 6px 18px rgba(15, 23, 36, 0.06);
}

/* <PERSON>gin Header */
.login-header { text-align: center; margin-bottom: 2rem; }
.login-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f1724;
    margin-bottom: 0.35rem;
}

.login-subtitle {
    color: #6b7280;
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Form Styles */
.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 0.95rem;
    border: 1px solid #e6e9ee;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.18s ease, box-shadow 0.18s ease;
    background-color: #fff;
}

.form-input:focus {
    outline: none;
    border-color: #2c3e50;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.form-input::placeholder {
    color: #999;
}

/* Button Styles */
.btn { display: block; width: 100%; padding: 0.85rem 1rem; border-radius: 8px; font-weight: 600; cursor: pointer; }
.btn-primary { background-color: #0f1724; color: #fff; border: none; font-size: 1rem; padding: 0.9rem 1rem; }
.btn-primary:hover { background-color: #111827; }

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    border-color: #ccc;
}

/* Divider */
.divider { text-align: center; margin: 1.25rem 0; color: #9ca3af; font-size: 0.9rem; position: relative; }
.divider::before, .divider::after { content: ''; position: absolute; top: 50%; width: 40%; height: 1px; background-color: #eef0f2; }
.divider::before { left: 0; }
.divider::after { right: 0; }

/* Forgot Password */
.forgot-password { text-align: center; margin-top: 1.25rem; }
.forgot-password a { color: #374151; text-decoration: none; font-size: 0.95rem; }
.forgot-password a:hover { text-decoration: underline; }

/* Bottom Footer */
/* small bottom row inside the page (language + links) */
.bottom-footer { display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: #6b7280; margin-top: 2.5rem; }

.language-selector {
    cursor: pointer;
    user-select: none;
}

.language-selector:hover {
    color: #333;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #2c3e50;
}

/* Form Validation Styles */
.form-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.field-error {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container { margin: 3.5rem 1rem; padding: 1.75rem; }
    .login-title { font-size: 1.25rem; }
    .bottom-footer { flex-direction: column; gap: 0.75rem; text-align: center; }
}

@media (max-width: 480px) {
    .login-container { margin: 2rem 0.75rem; padding: 1.25rem; }
    .login-title { font-size: 1.125rem; }
}
