/* Login Page Specific Styles - Matching the provided design */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    position: relative;
}

/* Top Left Logo */
.top-logo {
    position: absolute;
    top: 2rem;
    left: 2rem;
    display: flex;
    align-items: center;
    z-index: 10;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background-color: #2c3e50;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    color: #333;
}

/* Top Right Back Link */
.top-back-link {
    position: absolute;
    top: 2rem;
    right: 2rem;
    z-index: 10;
}

.back-link {
    text-decoration: none;
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #2c3e50;
}

/* Main Login Container */
.login-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 400px;
    background: white;
    padding: 3rem 2.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
}

.login-subtitle {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Form Styles */
.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-input:focus {
    outline: none;
    border-color: #2c3e50;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.form-input::placeholder {
    color: #999;
}

/* Button Styles */
.btn {
    display: block;
    width: 100%;
    padding: 0.875rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.btn-primary {
    background-color: #1a1a1a;
    color: white;
}

.btn-primary:hover {
    background-color: #333;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    border-color: #ccc;
}

/* Divider */
.divider {
    text-align: center;
    margin: 1.5rem 0;
    color: #666;
    font-size: 0.9rem;
    position: relative;
}

.divider::before,
.divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 45%;
    height: 1px;
    background-color: #ddd;
}

.divider::before {
    left: 0;
}

.divider::after {
    right: 0;
}

/* Forgot Password */
.forgot-password {
    text-align: center;
    margin-top: 1.5rem;
}

.forgot-password a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.forgot-password a:hover {
    text-decoration: underline;
}

/* Bottom Footer */
.bottom-footer {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    right: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #666;
}

.language-selector {
    cursor: pointer;
    user-select: none;
}

.language-selector:hover {
    color: #333;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #2c3e50;
}

/* Form Validation Styles */
.form-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.field-error {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-logo,
    .top-back-link {
        position: static;
        margin: 1rem;
    }
    
    .top-logo {
        text-align: left;
    }
    
    .top-back-link {
        text-align: right;
        margin-top: -3rem;
    }
    
    .login-container {
        position: static;
        transform: none;
        margin: 2rem 1rem;
        padding: 2rem 1.5rem;
    }
    
    .bottom-footer {
        position: static;
        margin: 2rem 1rem 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-links {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .login-container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
}
