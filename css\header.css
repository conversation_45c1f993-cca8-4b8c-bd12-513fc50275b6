/* Header Component Styles - tuned to match the screenshot */

/* Inherit global reset from styles.css; only component-specific rules here */
.header {
    background-color: #ffffff;
    border-bottom: 1px solid #eef0f2;
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Logo Styles */
.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #111827;
    font-family: 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background-color: #0f1724; /* dark navy similar to screenshot */
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    margin-right: 0.75rem;
    font-size: 1.05rem;
}

.logo-text {
    font-size: 1.125rem;
    font-weight: 700;
    letter-spacing: 0.4px;
}

.header-right {
    display: flex;
    align-items: center;
}

.back-link {
    text-decoration: none;
    color: #6b7280;
    font-size: 0.95rem;
    font-weight: 500;
    transition: color 0.15s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-link:hover {
    color: #374151;
}

@media (max-width: 768px) {
    .header-container { padding: 0.75rem 1rem; }
    .logo-text { font-size: 1rem; }
    .logo-icon { width: 36px; height: 36px; }
}

