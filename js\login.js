// Login page functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const ssoButton = document.getElementById('ssoLogin');

    // Handle login form submission
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            if (!UIHelpers.validateForm('loginForm')) {
                UIHelpers.showMessage('Please fill in all required fields.', 'error');
                return;
            }

            // Get form data
            const formData = new FormData(loginForm);
            const email = formData.get('email');
            const password = formData.get('password');

            // Show loading state
            const submitButton = loginForm.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Signing in...';
            submitButton.disabled = true;

            // Simulate login process (replace with actual authentication)
            setTimeout(() => {
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;

                // For demo purposes, redirect to dashboard
                if (email && password) {
                    UIHelpers.showMessage('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    UIHelpers.showMessage('Invalid credentials. Please try again.', 'error');
                }
            }, 2000);
        });
    }

    // Handle SSO login
    if (ssoButton) {
        ssoButton.addEventListener('click', function() {
            // Show loading state
            const originalText = ssoButton.textContent;
            ssoButton.textContent = 'Connecting to SSO...';
            ssoButton.disabled = true;

            // Simulate SSO process
            setTimeout(() => {
                // Reset button state
                ssoButton.textContent = originalText;
                ssoButton.disabled = false;

                // For demo purposes, show message
                UIHelpers.showMessage('SSO authentication would be handled here.', 'info');
            }, 2000);
        });
    }

    // Add input validation feedback
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('error');
            } else {
                this.classList.remove('error');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('error') && this.value.trim()) {
                this.classList.remove('error');
            }
        });
    });
});
