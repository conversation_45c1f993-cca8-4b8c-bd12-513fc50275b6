/* Dashboard Specific Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f6f7f8;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    color: #111827;
}

/* Layout */
html, body { height: 100%; }
body { display: flex; flex-direction: column; }

/* Header Styles for Dashboard */
.header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

/* Logo with subtitle */
.logo-text-container {
    display: flex;
    flex-direction: column;
}

.logo-text {
    font-size: 1.125rem;
    font-weight: 700;
    letter-spacing: 0.4px;
    line-height: 1.2;
}

.logo-subtitle {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 400;
    line-height: 1;
}

/* Search Bar */
.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 2rem;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    background: #f9fafb;
    color: #374151;
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
}

/* Header Right */
.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-icon {
    position: relative;
    padding: 8px;
    cursor: pointer;
    color: #6b7280;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.user-menu:hover {
    background-color: #f3f4f6;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
}

.dropdown-arrow {
    color: #9ca3af;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 2.5rem;
}

.welcome-title {
    font-size: 1.5rem;
    font-weight: 300;
    color: #111827;
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1rem;
    color: #6b7280;
    line-height: 1.5;
}

/* Hubs Grid */
.hubs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.hub-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.hub-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.hub-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background-color: #f9fafb;
    color: #374151;
}

.hub-content {
    flex: 1;
}

.hub-title {
    font-size: 1.125rem;
    font-weight: 400;
    color: #111827;
    margin-bottom: 0.5rem;
}

.hub-description {
    font-size: 0.9rem;
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.hub-button {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: #1f2937;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.hub-button:hover {
    background-color: #374151;
}

.hub-button.crisis-button {
    background-color: #1f2937;
}

/* Dashboard Bottom Section */
.dashboard-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1rem;
}

/* Recent Activity */
.recent-activity {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon.document {
    background-color: #f3f4f6;
    color: #6b7280;
}

.activity-icon.crisis-room {
    background-color: #fef3c7;
    color: #f59e0b;
}

.activity-icon.drill {
    background-color: #d1fae5;
    color: #10b981;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.activity-meta {
    font-size: 0.8rem;
    color: #6b7280;
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
}

.actions-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
}

.action-button:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.action-button svg {
    color: #6b7280;
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
    }

    .header-center {
        margin: 0 1rem;
        max-width: 200px;
    }

    .container {
        padding: 0 1rem;
    }

    .welcome-title {
        font-size: 1.5rem;
    }

    .hubs-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-bottom {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .user-name {
        display: none;
    }
}