<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COAST Platform - Dashboard</title>
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>

    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="index.html" class="logo">
                <div class="logo-icon">
                    <svg width="20" height="20" viewBox="0 0 512 512" fill="currentColor">
                        <path d="M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8V444.8C394 378 431.1 230.1 432 141.4L256 66.8l0 0z"/>
                    </svg>
                </div>
                <div class="logo-text-container">
                    <span class="logo-text">COAST Platform</span>
                    <span class="logo-subtitle">Emergency Response Org</span>
                </div>
            </a>

            <div class="header-center">
                <div class="search-container">
                    <svg class="search-icon" width="16" height="16" viewBox="0 0 512 512" fill="currentColor">
                        <path d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"/>
                    </svg>
                    <input type="text" placeholder="Search across all hubs..." class="search-input">
                </div>
            </div>

            <div class="header-right">
                <div class="notification-icon">
                    <svg width="20" height="20" viewBox="0 0 448 512" fill="currentColor">
                        <path d="M224 0c-17.7 0-32 14.3-32 32V51.2C119 66 64 130.6 64 208v18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416H416c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8V208c0-77.4-55-142-128-156.8V32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3H224 160c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z"/>
                    </svg>
                    <span class="notification-badge">3</span>
                </div>

                <div class="user-menu">
                    <img src="https://api.dicebear.com/7.x/notionists/svg?scale=200&seed=12345" alt="John Smith" class="user-avatar">
                    <span class="user-name">John Smith</span>
                    <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 320 512" fill="currentColor">
                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                    </svg>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="welcome-section">
                <h1 class="welcome-title">Welcome to COAST Platform</h1>
                <p class="welcome-subtitle">Your central hub for crisis management and collaboration. Choose a workspace to get started.</p>
            </div>

            <div class="hubs-grid">
                <div class="hub-card">
                    <div class="hub-icon collaboration">
                        <svg width="24" height="24" viewBox="0 0 640 512" fill="currentColor">
                            <path d="M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192h42.7c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0H21.3C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7h42.7C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3H405.3zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352H378.7C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7H154.7c-14.7 0-26.7-11.9-26.7-26.7z"/>
                        </svg>
                    </div>
                    <div class="hub-content">
                        <h3 class="hub-title">Collaboration Hub</h3>
                        <p class="hub-description">Files, chat, video, whiteboard tools for seamless team collaboration and communication.</p>
                        <button class="hub-button">Open Hub</button>
                    </div>
                </div>

                <div class="hub-card">
                    <div class="hub-icon crisis">
                        <svg width="24" height="24" viewBox="0 0 512 512" fill="currentColor">
                            <path d="M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224a32 32 0 1 0 -64 0 32 32 0 1 0 64 0z"/>
                        </svg>
                    </div>
                    <div class="hub-content">
                        <h3 class="hub-title">Crisis Management Hub</h3>
                        <p class="hub-description">Incidents, crisis rooms, tasks, SITREP management for active emergency response.</p>
                        <button class="hub-button crisis-button">Go to Crisis Hub</button>
                    </div>
                </div>

                <div class="hub-card">
                    <div class="hub-icon preparedness">
                        <svg width="24" height="24" viewBox="0 0 384 512" fill="currentColor">
                            <path d="M280 64h40c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128C0 92.7 28.7 64 64 64h40 9.6C121 27.5 153.3 0 192 0s71 27.5 78.4 64H280zM64 112c-8.8 0-16 7.2-16 16V448c0 8.8 7.2 16 16 16H320c8.8 0 16-7.2 16-16V128c0-8.8-7.2-16-16-16H304v24c0 13.3-10.7 24-24 24H192 104c-13.3 0-24-10.7-24-24V112H64zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"/>
                        </svg>
                    </div>
                    <div class="hub-content">
                        <h3 class="hub-title">Pre-Crisis Preparedness Hub</h3>
                        <p class="hub-description">Plans, exercises, playbooks, readiness assessments for proactive crisis preparation.</p>
                        <button class="hub-button">Open Preparedness Hub</button>
                    </div>
                </div>
            </div>

            <div class="dashboard-bottom">
                <div class="recent-activity">
                    <h2 class="section-title">My Recent Activity</h2>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon document">
                                <svg width="16" height="16" viewBox="0 0 384 512" fill="currentColor">
                                    <path d="M320 464c8.8 0 16-7.2 16-16V160H256c-17.7 0-32-14.3-32-32V48H64c-8.8 0-16 7.2-16 16V448c0 8.8 7.2 16 16 16H320zM0 64C0 28.7 28.7 0 64 0H229.5c17 0 33.3 6.7 45.3 18.7l90.5 90.5c12 12 18.7 28.3 18.7 45.3V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V64z"/>
                                </svg>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Emergency Response Plan v2.1</div>
                                <div class="activity-meta">Opened 2 hours ago</div>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon crisis-room">
                                <svg width="16" height="16" viewBox="0 0 512 512" fill="currentColor">
                                    <path d="M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32z"/>
                                </svg>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Crisis Room: Power Outage Response</div>
                                <div class="activity-meta">Last message 4 hours ago</div>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon drill">
                                <svg width="16" height="16" viewBox="0 0 384 512" fill="currentColor">
                                    <path d="M280 64h40c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128C0 92.7 28.7 64 64 64h40 9.6C121 27.5 153.3 0 192 0s71 27.5 78.4 64H280z"/>
                                </svg>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Fire Drill Exercise - Building A</div>
                                <div class="activity-meta">Completed yesterday</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h2 class="section-title">Quick Actions</h2>
                    <div class="actions-list">
                        <button class="action-button">
                            <svg width="16" height="16" viewBox="0 0 512 512" fill="currentColor">
                                <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM232 344V280H168c-13.3 0-24-10.7-24-24s10.7-24 24-24h64V168c0-13.3 10.7-24 24-24s24 10.7 24 24v64h64c13.3 0 24 10.7 24 24s-10.7 24-24 24H280v64c0 13.3-10.7 24-24 24s-24-10.7-24-24z"/>
                            </svg>
                            Start a Crisis Response
                        </button>

                        <button class="action-button">
                            <svg width="16" height="16" viewBox="0 0 512 512" fill="currentColor">
                                <path d="M288 109.3V352c0 17.7-14.3 32-32 32s-32-14.3-32-32V109.3l-73.4 73.4c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l128-128c12.5-12.5 32.8-12.5 45.3 0l128 128c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L288 109.3z"/>
                            </svg>
                            Upload a File
                        </button>

                        <button class="action-button">
                            <svg width="16" height="16" viewBox="0 0 448 512" fill="currentColor">
                                <path d="M128 0c17.7 0 32 14.3 32 32V64H288V32c0-17.7 14.3-32 32-32s32 14.3 32 32V64h48c26.5 0 48 21.5 48 48v48H0V112C0 85.5 21.5 64 48 64H96V32c0-17.7 14.3-32 32-32zM0 192H448V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V192zm64 80v32c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16V272c0-8.8-7.2-16-16-16H80c-8.8 0-16 7.2-16 16z"/>
                            </svg>
                            Schedule a Drill
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Footer -->
    <footer class="bottom-footer">
        <div class="footer-container">
            <div class="language-selector">
                <svg class="globe-icon" width="16" height="16" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.3c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64H348.7c2.2 20.4 3.3 41.8 3.3 64zm28.8-64H503.9c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0H18.6C48.6 85.9 112.2 29.1 190.6 8.4C165.1 42.6 145.3 96.1 135.3 160zM8.1 192H131.2c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64H8.1C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64zM194.7 446.6c-11.6-26-20.9-58.2-27-94.6H344.3c-6.1 36.4-15.5 68.6-27 94.6c-10.5 23.6-22.2 40.7-33.5 51.5C272.6 508.8 263.3 512 256 512s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6C112.2 482.9 48.6 426.1 18.6 352H135.3zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6H493.4z"/>
                </svg>
                <span class="language-text">English</span>
                <span class="dropdown-arrow">▼</span>
            </div>

            <div class="footer-links">
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
            </div>
        </div>
    </footer>

</body>
</html>